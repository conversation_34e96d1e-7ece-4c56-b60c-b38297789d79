const mongoose = require('mongoose');
const { Schema } = mongoose;

const applicantDetailsSchema = new Schema({
  // Define structure based on your requirements
  name: String,
  relation: String,
  percentageShare: Number
}, { _id: false });

const applicantSchema = new Schema({
  fname: {
    type: String,
    required: [true, 'First name is required']
  },
  mname: {
    type: String,
    default: ''
  },
  lname: {
    type: String,
    required: [true, 'Last name is required']
  },
  phoneCode: {
    type: String,
  },
  phoneNumber: {
    type: String,
    required: [true, 'Phone number is required']
  },
  alternatePhoneCode: {
    type: String,
  },
  alternatePhoneNumber: String,
  email: {
    type: String,
    required: [true, 'Email is required']
  },
  applicationType: {
    type: String,
    required: [true, 'Application type is required']
  },
  occupationType: {
    type: String,
    required: [true, 'Occupation type is required']
  },
  constitution: {
    type: String,
    required: [true, 'Constitution is required']
  },
  dateOfBusinessStart: {
    type: Date,
    required: [true, 'Business start date is required']
  },
  purposeOfLoan: {
    type: String,
    required: [true, 'Loan purpose is required']
  },
  applicantExperience: {
    type: Number,
    required: [true, 'Applicant experience is required']
  },
  applicantDetails: [applicantDetailsSchema],
  yearlyRepayment: {
    type: Number,
    required: [true, 'Yearly repayment is required']
  },
  totalYearlyRepayment: {
    type: Number,
    required: [true, 'Total yearly repayment is required']
  },
  avgYearlyIncome: {
    type: Number,
    required: [true, 'Average yearly income is required']
  },
  lastFinancialIncome: {
    type: Number,
    required: [true, 'Last financial income is required']
  },
  isDeleted: {
    type: Boolean,
    default: false
  }
}, { 
  timestamps: true
});

const Applicant = mongoose.model('primaryApplication', applicantSchema);
module.exports = Applicant;