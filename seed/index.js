require("dotenv").config();

const mongoose = require("mongoose");
const Country = require("../schema/country");
const State = require("../schema/state");
const City = require("../schema/city");
const LoanType = require("../schema/loanType");

const countries = require("./country");
const stateCityData = require("./stateCity");
const loanTypeData = require("./loanType");

const MONGO_URI = process.env.MONGODB_URI;

mongoose
  .connect(MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true })
  .then(async () => {
    console.log("Connected to DB");

    // 🟢 Upsert Countries
    for (const country of countries) {
      await Country.updateOne(
        { name: country.name },
        { $set: country },
        { upsert: true }
      );
    }

    // 🟢 Upsert Loan Types
    for (const loanType of loanTypeData) {
      await LoanType.updateOne(
        { name: loanType.name },
        { $set: loanType },
        { upsert: true }
      );
    }

    // 🟢 Upsert States and Cities
    for (const [stateName, cities] of Object.entries(stateCityData)) {
      const countryCode = "IN";

      const state = await State.findOneAndUpdate(
        { name: stateName, countryCode },
        { $set: { name: stateName, countryCode } },
        { upsert: true, new: true }
      );

      for (const cityName of cities) {
        await City.updateOne(
          { name: cityName, stateId: state._id },
          { $set: { name: cityName, stateId: state._id } },
          { upsert: true }
        );
      }
    }

    console.log("Non-destructive seeding completed ✅");
    process.exit();
  })
  .catch((err) => {
    console.error("Error during seeding:", err);
    process.exit(1);
  });
