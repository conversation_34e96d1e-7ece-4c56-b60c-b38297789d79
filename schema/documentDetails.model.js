const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const DocumentDetailsSchema = new Schema({
  primaryApplicationId: {
    type: Schema.Types.ObjectId,
    ref: "PrimaryApplication",
    required: true,
  },
  documents: [
    {
      documentName: { type: String, required: true },
      value: { type: String, required: true }, // Flexible object type
    },
  ],
});

module.exports = mongoose.model("DocumentDetails", DocumentDetailsSchema);
