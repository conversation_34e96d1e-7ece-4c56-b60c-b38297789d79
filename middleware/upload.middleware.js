const multer = require('multer');
const path = require('path');
const { ErrorHandler } = require('../Utils/common');

// Set up storage for uploaded files
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'uploads/');
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

// File filter to accept only images
const fileFilter = (req, file, cb) => {
    const filetypes = /jpe?g|png|gif/;
    const mimetype = filetypes.test(file.mimetype);
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());

    if (mimetype && extname) {
        return cb(null, true);
    }
    cb(new ErrorHandler('Only image files are allowed (jpg, jpeg, png, gif)', 400));
};

// Initialize multer with configuration
const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 1024 * 1024 * 5, // 5MB limit
        files: 1 // Allow only 1 file per request
    }
});

// Error handler for file uploads
const handleUploadError = (err, req, res, next) => {
    if (err instanceof multer.MulterError) {
        // A Multer error occurred when uploading
        if (err.code === 'LIMIT_FILE_SIZE') {
            return next(new ErrorHandler('File size too large. Maximum 5MB allowed.', 400));
        }
        if (err.code === 'LIMIT_FILE_COUNT') {
            return next(new ErrorHandler('Only one file is allowed', 400));
        }
        return next(new ErrorHandler('File upload error', 400));
    } else if (err) {
        // An unknown error occurred
        return next(err);
    }
    next();
};

module.exports = {
    upload,
    handleUploadError
};
