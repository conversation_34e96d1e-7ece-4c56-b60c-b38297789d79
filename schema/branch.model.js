const mongoose = require('mongoose');
const { Schema } = mongoose;

const branchSchema = new Schema({
    branch_name: {
        type: String,
        required: [true, 'Branch name is required'],
        trim: true,
    },
    branch_code: {
        type: String,
        required: [true, 'Branch code is required'],
        unique: true,
        uppercase: true,
    },
    branch_address: {
        type: String,
        required: [true, 'Branch address is required'],
    },
    branch_manager: {
        type: String,
        required: [true, 'Branch manager name is required'],
    },
    branch_manager_id: {
        type: String,
        required: [true, 'Manager ID is required'],
        unique: true
    },
    branch_status: {
        type: String,
        required: true,
        enum: {
            values: ['active', 'inactive', 'pending'],
            message: 'Status must be active, inactive, or pending'
        },
        default: 'pending'
    },
    isDeleted: {
        type: Boolean,
        default: false,
    }
}, { timestamps: true });

// Added index for frequently searched fields
// branchSchema.index({ branch_code: 1 }, { unique: true });
branchSchema.index({ branch_status: 1 });

const Branch = mongoose.model('Branch', branchSchema);

module.exports = Branch;