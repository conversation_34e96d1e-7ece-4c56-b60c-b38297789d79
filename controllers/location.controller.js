const Country = require("../schema/country");
const State = require("../schema/state");
const City = require("../schema/city");

const getCountries = async (req, res) => {
  try {
    const countries = await Country.find();
    res.status(200).json({ success: true, data: countries });
  } catch (error) {
    console.error("Error fetching countries:", err);
    res.status(500).json({ message: "Server Error" });
  }
};

const getState = async (req, res) => {
  const { countryCode } = req.params;

  try {
    const states = await State.find({ countryCode });
    res.status(200).json({ success: true, data: states });
  } catch (error) {
    console.error("Error fetching states:", err);
    res.status(500).json({ message: "Server Error" });
  }
};

const getCity = async (req, res) => {
  const { stateId } = req.params;

  try {
    const cities = await City.find({ stateId });
    res.status(200).json({ success: true, data: cities });
  } catch (error) {
    console.error("Error fetching cities:", err);
    res.status(500).json({ message: "Server Error" });
  }
};

module.exports = {
  getCountries,
  getState,
  getCity,
};
