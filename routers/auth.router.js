const express = require('express');
const router = express.Router();
const loginHandler = require('../controllers/login.controller');
const { authMiddleware } = require('../middleware/auth.middleware');
const userController = require('../controllers/user.controller');

/**
 * @swagger
 * /login:
 *   post:
 *     summary: Authenticate user and get JWT token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *     responses:
 *       200:
 *         description: Successful authentication
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 token:
 *                   type: string
 *       401:
 *         description: Invalid credentials
 */
router.post('/login', loginHandler.login);

/**
 * @swagger
 * /logout:
 *   get:
 *     summary: Logout user (invalidate token)
 *     tags: [Authentication]
 *     responses:
 *       200:
 *         description: Successfully logged out
 */
router.get('/logout', loginHandler.logout);

/**
 * @swagger
 * /auth/me:
 *   get:
 *     summary: Get current user's profile information
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Successfully retrieved user profile
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     email:
 *                       type: string
 *                     role:
 *                       type: string
 *                       enum: [superadmin, employee, manager, branchmanager, managingdirector]
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/me', authMiddleware, loginHandler.getCurrentUser);

/**
 * @swagger
 * /users:
 *   get:
 *     summary: Get all users (superadmin only)
 *     tags: [User Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: string
 *           enum: ['true', 'false']
 *         description: Filter users by active status (true/false)
 *     responses:
 *       200:
 *         description: List of users (filtered by status if specified)
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   email:
 *                     type: string
 *                   role:
 *                     type: string
 *                     enum: [superadmin, employee, manager, branchmanager, managingdirector]
 *                   isActive:
 *                     type: boolean
 *       403:
 *         description: Access denied. Super admin privileges required
 */
router.get('/users', authMiddleware, userController.isSuperAdmin, userController.getAllUsers);

/**
 * @swagger
 * /users:
 *   post:
 *     summary: Create a new user (superadmin only)
 *     tags: [User Management]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *               password:
 *                 type: string
 *               role:
 *                 type: string
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *     responses:
 *       201:
 *         description: User created successfully
 *       400:
 *         description: Invalid role or user already exists
 *       403:
 *         description: Access denied. Super admin privileges required
 */
router.post('/users', authMiddleware, userController.isSuperAdmin, userController.createUser);

/**
 * @swagger
 * /users/{userId}/status:
 *   patch:
 *     summary: Update user status (activate/deactivate) (superadmin only)
 *     tags: [User Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               isActive:
 *                 type: boolean
 *                 description: Set to true to activate, false to deactivate
 *     responses:
 *       200:
 *         description: User status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 user:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     email:
 *                       type: string
 *                     role:
 *                       type: string
 *                     isActive:
 *                       type: boolean
 *       400:
 *         description: Invalid isActive parameter
 *       404:
 *         description: User not found
 *       403:
 *         description: Access denied. Super admin privileges required
 */
router.patch('/users/:userId/status', authMiddleware, userController.isSuperAdmin, userController.updateUserStatus);

/**
 * @swagger
 * /users/{userId}/role:
 *   put:
 *     summary: Update user role (superadmin only)
 *     tags: [User Management]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               newRole:
 *                 type: string
 *     responses:
 *       200:
 *         description: User role updated successfully
 *       400:
 *         description: Invalid role
 *       404:
 *         description: User not found
 *       403:
 *         description: Access denied. Super admin privileges required
 */
router.put('/users/:userId/role', authMiddleware, userController.isSuperAdmin, userController.updateUserRole);

// Export router
module.exports = router;
