const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const CollateralDetailsSchema = new Schema({
  primaryApplicationId: {
    type: Schema.Types.ObjectId,
    ref: "PrimaryApplication",
    required: true,
  },
  propertyOwnerName: { type: String, required: true },
  propertyAddress: { type: String, required: true },
  propertyType: { type: String, required: true },
  landArea: { type: String, required: true },
  constructionArea: { type: String, required: true },
  propertyValuation: { type: String, required: true },
  valuationDate: { type: String, required: true },
});

module.exports = mongoose.model("CollateralDetails", CollateralDetailsSchema);
