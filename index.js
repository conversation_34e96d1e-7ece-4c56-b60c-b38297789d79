const express = require("express");
const app = express();
const cors = require("cors");
const setupSwagger = require("./swagger");
const routes = require("./api");
const morgan = require("morgan");
const cookieParser = require("cookie-parser");
const mongoSanitize = require("express-mongo-sanitize");
const connectToDatabase = require("./database/connection");
require("dotenv").config();

const corsOptions = {
  origin: process.env.FRONTEND_URL,
  methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"],
};

async function main() {
  morgan.token("datetime", () => {
    const now = new Date();
    return now.toISOString(); // Format: YYYY-MM-DDTHH:mm:ss.sssZ
  });

  const customFormat =
    ":datetime :method :url :status :response-time ms - :res[content-length]";

  app.use(morgan(customFormat));

  app.use(cors(corsOptions));
  setupSwagger(app);

  app.use(morgan("dev"));
  app.use(express.json());
  app.use(mongoSanitize());
  app.use(express.urlencoded({ extended: true }));
  app.use(cookieParser());
  app.use(routes);

  const db = await connectToDatabase();

  app.listen(3000, () => {
    console.log(`Server running on ${process.env.PORT}`);
  });
}

main();
