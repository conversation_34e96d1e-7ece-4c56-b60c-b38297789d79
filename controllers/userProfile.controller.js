const User = require('../models/user.model');
const { Error<PERSON>and<PERSON> } = require('../Utils/common');
const { uploadFile, deleteFile } = require('../services/fileUpload.service');

/**
 * Register a new user (admin only)
 * @route POST /api/users
 * @access Private/Admin
 */
exports.registerUser = async (req, res, next) => {
    try {
        const userData = req.body;
        
        // Check if user already exists
        const existingUser = await User.findOne({ email: userData.email });
        if (existingUser) {
            throw new ErrorHandler('User with this email already exists', 400);
        }

        // Create new user
        const user = new User({
            ...userData,
            createdBy: req.user.userId,
            updatedBy: req.user.userId
        });

        await user.save();
        
        // Remove sensitive data before sending response
        user.password = undefined;
        user.__v = undefined;

        res.status(201).json({
            success: true,
            message: 'User registered successfully',
            data: user
        });
    } catch (error) {
        next(error);
    }
};

/**
 * Get current user's profile
 * @route GET /api/users/profile
 * @access Private
 */
exports.getMyProfile = async (req, res, next) => {
    try {
        const user = await User.findById(req.user.userId)
            .select('-password -__v')
            .populate('branch', 'name code address')
            .lean();

        if (!user) {
            return next(new ErrorHandler('User not found', 404));
        }

        res.status(200).json({
            success: true,
            data: user
        });
    } catch (error) {
        next(error);
    }
};

/**
 * Update current user's profile
 * @route PUT /api/users/profile
 * @access Private
 */
exports.updateMyProfile = async (req, res, next) => {
    try {
        // Get updates from validated request body
        const updates = req.body;
        updates.updatedBy = req.user.userId;
        updates.updatedAt = new Date();
        
        // Remove any fields that shouldn't be updated through this endpoint
        const restrictedFields = ['password', 'role', 'isActive', 'email'];
        restrictedFields.forEach(field => delete updates[field]);

        const user = await User.findByIdAndUpdate(
            req.user.userId,
            { $set: updates },
            { new: true, runValidators: true }
        ).select('-password -__v');

        if (!user) {
            return next(new ErrorHandler('User not found', 404));
        }

        res.status(200).json({
            success: true,
            message: 'Profile updated successfully',
            data: user
        });
    } catch (error) {
        next(error);
    }
};

/**
 * Change user's password
 * @route PATCH /api/users/password
 * @access Private
 */
exports.changePassword = async (req, res, next) => {
    try {
        const { currentPassword, newPassword } = req.body;

        // Get user with password field
        const user = await User.findById(req.user.userId).select('+password');
        
        if (!user) {
            return next(new ErrorHandler('User not found', 404));
        }

        // Check current password
        const isMatch = await user.comparePassword(currentPassword);
        if (!isMatch) {
            return next(new ErrorHandler('Current password is incorrect', 400));
        }

        // Update password
        user.password = newPassword;
        user.updatedBy = req.user.userId;
        await user.save();

        // Invalidate all tokens (optional - implement token blacklist)
        // await Token.deleteMany({ user: user._id });


        res.status(200).json({
            success: true,
            message: 'Password updated successfully'
        });
    } catch (error) {
        next(error);
    }
};

/**
 * Upload profile picture
 * @route POST /api/users/profile/picture
 * @access Private
 */
exports.uploadProfilePicture = async (req, res, next) => {
    try {
        if (!req.file) {
            return next(new ErrorHandler('Please upload a file', 400));
        }

        // Upload to S3 or local storage
        // Delete old profile picture if it exists and is not the default
        if (req.user.profilePicture && !req.user.profilePicture.includes('default-avatar')) {
            await deleteFile(req.user.profilePicture);
        }

        // Upload new profile picture
        const result = await uploadFile(req.file);

        // Update user's profile picture
        const user = await User.findByIdAndUpdate(
            req.user.userId,
            { 
                profilePicture: result.url,
                updatedBy: req.user.userId,
                updatedAt: new Date()
            },
            { new: true, runValidators: true }
        ).select('-password -__v');

        res.status(200).json({
            success: true,
            message: 'Profile picture uploaded successfully',
            data: {
                profilePicture: user.profilePicture
            }
        });
    } catch (error) {
        next(error);
    }
};

/**
 * Get user by ID (Admin only)
 * @route GET /api/users/:id
 * @access Private/Admin
 */
exports.getUserById = async (req, res, next) => {
    try {
        const user = await User.findById(req.params.id)
            .select('-password -__v')
            .populate('branch', 'name code')
            .lean();

        if (!user) {
            return next(new ErrorHandler('User not found', 404));
        }

        // Check if the request is from admin or the user themselves
        if (req.user.role !== 'superadmin' && req.user.userId !== user._id.toString()) {
            return next(new ErrorHandler('Not authorized to access this resource', 403));
        }

        res.status(200).json({
            success: true,
            data: user
        });
    } catch (error) {
        next(error);
    }
};

/**
 * Update user (Admin only)
 * @route PUT /api/users/:id
 * @access Private/Admin
 */
exports.updateUser = async (req, res, next) => {
    try {
        // Only superadmin can update other users
        if (req.user.role !== 'superadmin' && req.user.userId !== req.params.id) {
            return next(new ErrorHandler('Not authorized to update this user', 403));
        }

        // Fields that can be updated
        const allowedUpdates = [
            'firstName', 'middleName', 'lastName', 'email', 'phoneNumber',
            'role', 'branch', 'department', 'designation', 'isActive'
        ];
        
        const updates = {};
        Object.keys(req.body).forEach(field => {
            if (allowedUpdates.includes(field)) {
                updates[field] = req.body[field];
            }
        });

        // If no valid updates
        if (Object.keys(updates).length === 0) {
            return next(new ErrorHandler('No valid fields to update', 400));
        }

        // Add updatedBy and updatedAt
        updates.updatedBy = req.user.userId;
        updates.updatedAt = new Date();

        const user = await User.findByIdAndUpdate(
            req.params.id,
            { $set: updates },
            { new: true, runValidators: true }
        ).select('-password -__v');

        if (!user) {
            return next(new ErrorHandler('User not found', 404));
        }

        res.status(200).json({
            success: true,
            message: 'User updated successfully',
            data: user
        });
    } catch (error) {
        next(error);
    }
};
