const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const LoanDetailsSchema = new Schema({
  primaryApplicationId: {
    type: Schema.Types.ObjectId,
    ref: "PrimaryApplication",
    required: true,
  },
  gstNumber: { type: String, required: true },
  panNumber: { type: String, required: true },
  businessRegistrationNumber: { type: String, required: true },
  moratoriumPush: { type: String, required: true },
  businessSector: { type: String, required: true },
  typeOfLoan: { type: String, required: true },
  loanAmount: { type: String, required: true },
  interestType: { type: String, required: true },
  rateOfInterest: { type: String, required: true },
});

module.exports = mongoose.model("LoanDetails", LoanDetailsSchema);
