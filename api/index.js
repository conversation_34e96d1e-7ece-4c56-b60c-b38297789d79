const express = require('express');
const router = express.Router();

// Import route files
const authRoutes = require('../routers/auth.router');
const branchRoutes = require('../routers/branch.router');
const applicationRoutes = require('../routers/application.router');
const userRoutes = require('../routers/user.router');

// Mount routes
router.use('/auth', authRoutes);
router.use('/branches', branchRoutes);
router.use('/applications', applicationRoutes);
router.use('/users', userRoutes);

// Health check endpoint
router.get('/health', (req, res) => {
    res.status(200).json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development'
    });
});

// 404 handler for API routes
router.use((req, res) => {
    res.status(404).json({
        success: false,
        error: 'API endpoint not found'
    });
});

module.exports = router;
