const mongoose = require("mongoose");

// MongoDB URI with database name
const uri = process.env.MONGODB_URI;

// Connection options
const options = {
  serverSelectionTimeoutMS: 5000, // Keep low for development
};

console.log("uri >> ", uri);

function connectToDatabase() {
  // Return existing connection if already connected
  if (mongoose.connection.readyState === 1) {
    return Promise.resolve(mongoose.connection);
  }

  return mongoose.connect(uri, options);
}

// Optional: Add event listeners for better monitoring
mongoose.connection.on("connected", () => {
  console.log("Mongoose connection established");
});

mongoose.connection.on("error", (err) => {
  console.error("Mongoose connection error:", err);
});

mongoose.connection.on("disconnected", () => {
  console.log("Mongoose connection disconnected");
});

// For proper shutdown (Ctrl+C)
process.on("SIGINT", async () => {
  await mongoose.connection.close();
  console.log("Mongoose connection closed through app termination");
  process.exit(0);
});

module.exports = connectToDatabase;
