{"name": "los-backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "seed": "node seed/index.js", "dev": "cross-env DOTENV_CONFIG_PATH=.env nodemon -r dotenv/config", "staging": "cross-env DOTENV_CONFIG_PATH=.env.staging node -r dotenv/config index.js", "production": "cross-env DOTENV_CONFIG_PATH=.env.production node -r dotenv/config index.js", "api": "node api/index.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "docker": "^1.0.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-mongo-sanitize": "^2.2.0", "express-validator": "^7.2.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "logger": "^0.0.1", "mongoose": "8.12.1", "mongoose-dynamic-schemas": "1.2.6", "morgan": "^1.10.0", "nodemon": "^3.1.9", "swagger": "^0.7.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "^3.17.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^22.13.10", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-node": "^11.1.0"}}